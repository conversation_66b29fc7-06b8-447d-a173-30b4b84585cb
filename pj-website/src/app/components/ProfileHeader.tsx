import Image from 'next/image';

interface ProfileHeaderProps {
  name: string;
  bio: string;
  previousRoles?: string;
  ctaText?: string;
  ctaLink?: string;
  avatarUrl?: string;
}

export default function ProfileHeader({
  name,
  bio,
  previousRoles,
  ctaText,
  ctaLink,
  avatarUrl
}: ProfileHeaderProps) {
  return (
    <div className="mb-16 text-center">
      <div className="flex flex-col items-center gap-4 mb-4">
        {avatarUrl && (
          <div className="relative w-16 h-16 overflow-hidden rounded-full border-2 border-white/20">
            <Image
              src={avatarUrl}
              alt={name}
              fill
              className="object-cover"
            />
          </div>
        )}
        <div>
          <h1 className="text-4xl font-bold tracking-tight mb-2 font-geist-sans text-[#536878]">
            {name}
          </h1>
          {bio && (
            <p className="text-xl text-gray-800 mb-4 font-light leading-relaxed">
              {bio}
            </p>
          )}
          {previousRoles && (
            <p className="text-lg text-gray-700 font-light leading-relaxed">
              Previously: {previousRoles}
            </p>
          )}
        </div>
      </div>

      {ctaText && ctaLink && (
        <a
          href={ctaLink}
          className="inline-block text-lg text-gray-700 hover:text-gray-900 transition-colors underline decoration-2 underline-offset-4"
        >
          {ctaText}
        </a>
      )}
    </div>
  );
}
