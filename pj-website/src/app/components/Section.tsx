import Link from 'next/link';
import { ReactNode } from 'react';

interface SectionProps {
  title: string;
  children: ReactNode;
  viewAllLink?: string;
  backgroundColor?: string;
  opacity?: string;
}

export default function Section({
  title,
  children,
  viewAllLink,
  backgroundColor = 'white',
  opacity = '0.06'
}: SectionProps) {
  return (
    <div className="relative mb-12 rounded-2xl overflow-hidden">
      {/* Background overlay */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundColor,
          opacity
        }}
      />

      {/* Content with padding and relative positioning */}
      <div className="relative z-10 p-8">
        <div className="flex flex-col items-center mb-8">
          <h2 className="text-xl md:text-xl font-bold text-center mb-2 text-[#536878] tracking-tight">
            {title}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-[#2F80ED] to-[#56CCF2] rounded-full" />
          {viewAllLink && (
            <Link href={viewAllLink} className="text-sm text-gray-600 hover:text-gray-900 mt-2">
              View all →
            </Link>
          )}
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6  gap-5">
          {children}
        </div>
      </div>
    </div>
  );
}
