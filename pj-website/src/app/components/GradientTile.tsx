import Link from 'next/link';

interface GradientTileProps {
  title: string;
  description: string;
  link: string;
  gradientFrom: string;
  gradientTo: string;
}

export default function GradientTile({ 
  title, 
  description, 
  link, 
  gradientFrom, 
  gradientTo 
}: GradientTileProps) {
  return (
    <Link 
      href={link}
      target="_blank"
      rel="noopener noreferrer"
      className="block group"
    >
      <div className="flex flex-col h-full">
        <div 
          className={`w-16 h-16 rounded-lg mb-3 transition-transform group-hover:scale-105`}
          style={{ 
            background: `linear-gradient(135deg, ${gradientFrom}, ${gradientTo})` 
          }}
        />
        <h3 className="text-base font-medium text-gray-900 mb-1">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </Link>
  );
}
