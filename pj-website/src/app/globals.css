@import "tailwindcss";

:root {
  --background: #1A1D23;  /* Dark navy background */
  --foreground: #ededed;
}

/* Theme variables */
:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1A1D23;  /* Keep dark navy in dark mode */
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

html, body {
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}

/* Add scroll margin to sections to account for fixed header */
section[id] {
  scroll-margin-top: 4rem;
}

body {
  background: var(--background);  /* Use the dark navy background */
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
  position: relative;
}

/* Vertical writing mode for sidebar navigation */
.writing-vertical-lr {
  writing-mode: vertical-lr;
  text-orientation: mixed;
}