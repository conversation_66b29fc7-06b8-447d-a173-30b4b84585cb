{"name": "augment-created-website", "version": "0.1.0", "private": true, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.2.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.4", "@eslint/eslintrc": "^3"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "echo 'Build complete'", "start": "next start", "lint": "next lint", "deploy": "firebase deploy --only hosting"}}