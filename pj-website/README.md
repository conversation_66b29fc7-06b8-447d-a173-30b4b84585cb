# Personal Website with Substack Blog Integration

This is a [Next.js](https://nextjs.org) project that showcases a personal website with colored gradient tiles linking to Substack blog posts.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Substack Integration

The colored tiles on the homepage link to Substack blog posts. To update the blog posts:

1. Edit the `blogPosts` array in `src/app/page.tsx`
2. Update the title, description, link, and gradient colors for each post
3. The links should point to your Substack blog posts

## Deploying to Firebase

### Prerequisites

1. Install Firebase CLI:
   ```bash
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```bash
   firebase login
   ```

3. Initialize your Firebase project (if not already done):
   ```bash
   firebase init
   ```
   - Select "Hosting: Configure files for Firebase Hosting"
   - Select your Firebase project
   - Use "build" as your public directory
   - Configure as a single-page app: Yes
   - Set up automatic builds and deploys with GitHub: No (or Yes if you want to)

### Build and Deploy

1. Build your Next.js app:
   ```bash
   npm run build
   ```

2. Deploy to Firebase:
   ```bash
   firebase deploy
   ```

## Customization

- Update the profile information in `src/app/page.tsx`
- Modify the gradient colors for each tile
- Add or remove sections as needed

## Learn More

To learn more about Next.js and Firebase, check out the following resources:

- [Next.js Documentation](https://nextjs.org/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Substack API Documentation](https://substack.com/api)
